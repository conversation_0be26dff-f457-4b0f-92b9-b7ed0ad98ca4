# PWM-速度转换系数标定调试指南

## 概述
本文档说明如何使用新增的调试代码来确定PWM控制值与实际速度之间的正确转换系数。

## 功能特性

### 1. 实时调试信息
- 显示目标PWM值和实际速度
- 计算实时转换系数
- 显示PWM占空比和速度比率

### 2. 自动标定模式
- 自动测试8个不同的PWM值：50, 100, 150, 200, 250, 300, 350, 400
- 每个PWM值测试50个周期，采集20个有效样本
- 自动计算平均速度和转换系数

### 3. 完整标定报告
- 生成表格形式的标定结果
- 计算推荐的转换系数
- 便于分析和决策

## 使用方法

### 步骤1：启用标定模式
在 `pid_app.c` 中确保：
```c
bool calibration_mode = true;  // 设为true启用标定
```

### 步骤2：编译并下载程序
1. 编译项目
2. 下载到STM32
3. 打开串口调试助手（波特率115200）

### 步骤3：观察调试输出
程序会自动输出以下信息：

#### 实时调试信息
```
=== PWM-Speed Calibration Debug ===
Target PWM: L=200.0, R=200.0
Actual Speed(cm/s): L=19.850, R=20.120
Conversion Factor: L=10.075, R=9.940
PWM Duty Cycle: L=20.0%, R=20.0%
Speed/Duty Ratio: L=0.993, R=1.006
=====================================
```

#### 标定结果
```
*** CALIBRATION RESULT ***
PWM: 200, Avg Speed: L=19.876, R=20.045 cm/s
Conversion Factor: L=10.062, R=9.978
Samples: 20
*************************
```

#### 完整报告
```
========================================
       PWM-SPEED CALIBRATION REPORT    
========================================
PWM  | Duty% | Speed_L | Speed_R | Factor_L | Factor_R
----|------|--------|--------|---------|--------
 50  |  5.0% |  4.850  |  4.920  |  10.309  |  10.163
100  | 10.0% |  9.750  |  9.880  |  10.256  |  10.121
150  | 15.0% | 14.920  | 15.100  |  10.054  |   9.934
200  | 20.0% | 19.876  | 20.045  |  10.062  |   9.978
250  | 25.0% | 24.850  | 25.120  |  10.060  |   9.952
300  | 30.0% | 29.750  | 30.200  |  10.084  |   9.934
350  | 35.0% | 34.920  | 35.450  |  10.023  |   9.873
400  | 40.0% | 39.850  | 40.320  |  10.038  |   9.921
----|------|--------|--------|---------|--------
AVG  |      |        |        |  10.111  |   9.985
========================================
RECOMMENDED CONVERSION FACTOR: 10.048
========================================
```

### 步骤4：分析结果
1. **查看转换系数的一致性**：如果各个PWM值的转换系数相近，说明线性关系良好
2. **检查左右轮差异**：左右轮的转换系数应该接近
3. **确定推荐值**：使用报告中的推荐转换系数

### 步骤5：应用结果
根据标定结果，在代码中使用推荐的转换系数：
```c
// 使用标定得到的转换系数
float converted_speed_left = filtered_speed_left * 10.048f;
float converted_speed_right = filtered_speed_right * 10.048f;
```

## 关闭标定模式
标定完成后，设置：
```c
bool calibration_mode = false;  // 关闭标定模式
```

## 注意事项

### 1. 测试环境
- 确保电机能够自由转动
- 避免外部阻力影响
- 保持电源电压稳定

### 2. 数据有效性
- 只有当速度 > 0.5 cm/s 时才计算转换系数
- 每个PWM值采集20个有效样本
- 自动过滤异常数据

### 3. 安全考虑
- 测试过程中注意电机转速
- 如有异常立即断电
- 确保机械结构安全

## 故障排除

### 问题1：转换系数变化很大
**原因**：机械阻力不一致或电源电压波动
**解决**：检查机械结构，稳定电源

### 问题2：左右轮系数差异大
**原因**：电机特性不同或机械装配问题
**解决**：分别使用不同的转换系数

### 问题3：低速时系数异常
**原因**：低速时编码器精度不足
**解决**：只使用中高速段的标定结果

## 输出文件
标定完成后，建议保存串口输出的完整报告，作为系统参数记录。