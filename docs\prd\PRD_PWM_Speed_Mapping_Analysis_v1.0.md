# PWM-速度映射关系分析报告

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-28
- **负责人**: Emma (产品经理)
- **项目**: STM32 PID电机控制系统

## 背景与问题陈述

### 问题描述
用户对PWM控制值(basic_speed)与编码器反馈速度(cm/s)之间的对应关系存在困惑：
1. basic_speed=200时对应20%占空比，但编码器反馈约20cm/s
2. 转换系数10.0f的物理意义不明确
3. PWM值与实际物理速度的映射关系需要澄清

### 核心痛点
- PWM控制值与物理速度单位不统一
- 转换系数缺乏理论依据
- 不同工况下映射关系的准确性未知

## 目标与成功指标

### 项目目标
1. **澄清映射关系**: 明确PWM控制值与实际速度的对应关系
2. **解释转换系数**: 说明10.0f系数的来源和物理意义
3. **提供优化方案**: 给出提高映射精度的改进建议

### 关键结果(KRs)
- KR1: 完成PWM-速度映射关系的完整分析文档
- KR2: 提供至少3种优化方案供选择
- KR3: 建立标定方法和数据记录格式

### 反向指标
- 不增加系统复杂度超过20%
- 不影响现有PID控制的稳定性

## 技术分析结果

### 系统架构分析
```
PID目标值(basic_speed) → PWM占空比 → 电机驱动 → 物理转速 → 编码器反馈(speed_cm_s) → 单位转换 → PID反馈值
```

### 关键参数解析
- **PWM配置**: TIM1, ARR=999, basic_speed=200 → 20%占空比
- **编码器计算**: speed_cm_s = (count/1456) * 20.42 / 0.005
- **转换系数**: 10.0f建立PWM单位与物理速度的映射

### 转换系数物理意义
- **实测基础**: PWM=20% → 实际速度≈20cm/s
- **线性假设**: PWM占空比(%) ≈ 实际速度(cm/s)
- **数学关系**: converted_speed = speed_cm_s * 10.0f

## 优化方案

### 方案A: 静态标定优化
- 建立多点标定表
- 适用于固定工况的应用
- 实现简单，资源消耗低

### 方案B: 动态自适应标定
- 运行时计算转换系数
- 适应不同负载和工况
- 复杂度较高，需要额外计算

### 方案C: 分段线性插值
- 不同PWM范围使用不同转换系数
- 平衡精度和复杂度
- 推荐方案

## 标定方法

### 实测标定步骤
1. 设置不同的basic_speed值（50, 100, 150, 200, 250, 300...）
2. 记录对应的实际速度speed_cm_s
3. 计算每个点的转换系数：factor = basic_speed / speed_cm_s
4. 分析转换系数的变化规律
5. 建立查找表或拟合函数

### 标定数据记录格式
```
PWM值 | 占空比(%) | 实测速度(cm/s) | 转换系数 | 备注
-----|----------|---------------|---------|-----
100  | 10%      | 8.5           | 11.76   | 低速段
200  | 20%      | 20.0          | 10.00   | 当前设定
300  | 30%      | 28.5          | 10.53   | 中速段
```

## 结论与建议

### 核心问题解答
1. **basic_speed的本质**: PWM定时器比较值，不是物理速度
2. **转换系数来源**: 基于实测的PWM-速度线性映射关系
3. **映射关系**: 通过实际硬件测试建立，非理论计算

### 立即可行的改进
- 保持当前10.0f系数，系统已经工作正常
- 添加标定功能，支持运行时调整
- 记录不同工况下的实际性能数据

### 长期优化方向
- 实施多点标定，提高不同速度段的精度
- 考虑负载自适应调整
- 建立完整的电机特性数据库