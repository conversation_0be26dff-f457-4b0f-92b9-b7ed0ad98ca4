--cpu=Cortex-M4.fp.sp
"24h_practice\startup_stm32f407xx.o"
"24h_practice\main.o"
"24h_practice\gpio.o"
"24h_practice\dma.o"
"24h_practice\i2c.o"
"24h_practice\tim.o"
"24h_practice\usart.o"
"24h_practice\stm32f4xx_it.o"
"24h_practice\stm32f4xx_hal_msp.o"
"24h_practice\stm32f4xx_hal_i2c.o"
"24h_practice\stm32f4xx_hal_i2c_ex.o"
"24h_practice\stm32f4xx_hal_rcc.o"
"24h_practice\stm32f4xx_hal_rcc_ex.o"
"24h_practice\stm32f4xx_hal_flash.o"
"24h_practice\stm32f4xx_hal_flash_ex.o"
"24h_practice\stm32f4xx_hal_flash_ramfunc.o"
"24h_practice\stm32f4xx_hal_gpio.o"
"24h_practice\stm32f4xx_hal_dma_ex.o"
"24h_practice\stm32f4xx_hal_dma.o"
"24h_practice\stm32f4xx_hal_pwr.o"
"24h_practice\stm32f4xx_hal_pwr_ex.o"
"24h_practice\stm32f4xx_hal_cortex.o"
"24h_practice\stm32f4xx_hal.o"
"24h_practice\stm32f4xx_hal_exti.o"
"24h_practice\stm32f4xx_hal_tim.o"
"24h_practice\stm32f4xx_hal_tim_ex.o"
"24h_practice\stm32f4xx_hal_uart.o"
"24h_practice\system_stm32f4xx.o"
"24h_practice\ringbuffer.o"
"24h_practice\pid.o"
"24h_practice\encoder_driver.o"
"24h_practice\motor_driver.o"
"24h_practice\uart_app.o"
"24h_practice\motor_app.o"
"24h_practice\encoder_app.o"
"24h_practice\pid_app.o"
"24h_practice\scheduler.o"
"24h_practice\scheduler_task.o"
--library_type=microlib --strict --scatter "24H_practice\24H_practice.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "24H_practice.map" -o 24H_practice\24H_practice.axf