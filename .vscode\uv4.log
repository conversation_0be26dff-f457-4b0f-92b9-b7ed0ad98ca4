*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\keil5\ARM\ARMCompiler_506_Windows_x86_b960\Bin'
Build target '24H_practice'
compiling encoder_app.c...
compiling uart_app.c...
compiling motor_driver.c...
compiling pid_app.c...
compiling motor_app.c...
compiling encoder_driver.c...
compiling Scheduler.c...
compiling Scheduler_Task.c...
compiling main.c...
linking...
Program Size: Code=18606 RO-data=458 RW-data=180 ZI-data=2652  
FromELF: creating hex file...
"24H_practice\24H_practice.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:12
